<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <title>firstPersonControl example</title>
        <style>
            #map {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
            }

            #debug-panel {
                position: fixed;
                bottom: 20px;
                left: 20px;
                background: rgba(0, 0, 0, 0.8);
                color: #00ff00;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                padding: 10px;
                border-radius: 5px;
                border: 1px solid #333;
                min-width: 200px;
                z-index: 1000;
                user-select: none;
            }

            #debug-panel .debug-title {
                color: #ffffff;
                font-weight: bold;
                margin-bottom: 5px;
                border-bottom: 1px solid #333;
                padding-bottom: 3px;
            }

            #debug-panel .debug-item {
                margin: 3px 0;
                display: flex;
                justify-content: space-between;
            }

            #debug-panel .debug-label {
                color: #cccccc;
            }

            #debug-panel .debug-value {
                color: #00ff00;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div id="map"></div>
        <div id="debug-panel">
            <div class="debug-title">Performance Monitor</div>
            <div class="debug-item">
                <span class="debug-label">FPS:</span>
                <span class="debug-value" id="fps-value">0</span>
            </div>
            <div class="debug-item">
                <span class="debug-label">Used JS Heap:</span>
                <span class="debug-value" id="used-heap-value">0 MB</span>
            </div>
            <div class="debug-item">
                <span class="debug-label">Total JS Heap:</span>
                <span class="debug-value" id="total-heap-value">0 MB</span>
            </div>
            <div class="debug-item">
                <span class="debug-label">JS Heap Limit:</span>
                <span class="debug-value" id="heap-limit-value">0 MB</span>
            </div>
        </div>
        <script type="module" src="firstPersonControl.js"></script>
    </body>
</html>
